{"name": "midway-quick-start", "version": "1.0.0", "description": "", "private": true, "dependencies": {"@midwayjs/bootstrap": "^3.12.0", "@midwayjs/core": "^3.12.0", "@midwayjs/info": "^3.12.0", "@midwayjs/koa": "^3.12.0", "@midwayjs/logger": "^3.1.0", "@midwayjs/static-file": "^3.4.12", "@midwayjs/typeorm": "^3.20.11", "@midwayjs/validate": "^3.20.13", "@midwayjs/view-nunjucks": "^3.4.12", "mysql": "^2.18.1", "typeorm": "^0.3.26"}, "devDependencies": {"@midwayjs/mock": "^3.12.0", "@types/jest": "^29.2.0", "@types/koa": "^2.13.4", "@types/node": "14", "cross-env": "^6.0.0", "jest": "^29.2.2", "mwts": "^1.3.0", "mwtsc": "^1.4.0", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "engines": {"node": ">=12.0.0"}, "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local mwtsc --watch --run @midwayjs/mock/app.js", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix", "ci": "npm run cov", "build": "mwtsc --cleanOutDir"}, "repository": {"type": "git", "url": ""}, "author": "anonymous", "license": "MIT"}