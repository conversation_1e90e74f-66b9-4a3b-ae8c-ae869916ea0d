# small-component-node-server
## Description
小组件的本地node服务，用来测试小组件render函数的正确性。

## Usage

```bash
$ nvm use 18.20.8
$ npm i
$ npm run dev
# $ npm run test
```

## How To Generator Entity
npx mdl-gen-midway -h localhost -p 3306 -d yourdbname -u root -x yourpassword -e mysql --noConfig --case-property camel

## How To Write DSL render Function
1. 在src/dsl目录下创建并编写对应小组件的ts代码
2. 在src/dsl/index.ts中分发对应小组件的function
