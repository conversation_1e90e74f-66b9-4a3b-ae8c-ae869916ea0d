import { changeCommonWidgetJson, changeNodePosition, changeNodeWidth, hideNodes, replaceImage, replaceText, showNode } from "./common";
import { EnumDesktopWidgetSize } from "./constant";

export function changePetWidgetJson(viewJson: string, appId: string, type: number, size: number, dataJson: JSON) {
  let viewJsonObj = JSON.parse(viewJson);
  changeCommonWidgetJson(viewJsonObj, appId, type, dataJson);

  if (size == EnumDesktopWidgetSize.Medium) {
    commonPetParse(viewJsonObj, dataJson, false);
  } else if (size == EnumDesktopWidgetSize.Large) {
    commonPetParse(viewJsonObj, dataJson, true);
  }

  return viewJsonObj;
}



function commonPetParse(json: JSON, dataSource: JSON, isLarge: boolean): any {
  // 布局设置
  hideNodes(['layout1','layout2', 'layout3', 'layout4'], json);
  if (!dataSource["itemList"] || dataSource["itemList"].length == 0) {
    // 什么都没选
    showNode('layout4', json);
  } else if (dataSource["itemList"].includes(1001) && dataSource["itemList"].includes(1003)) {
    showNode('layout1', json);
  } else if (dataSource["itemList"].includes(1002) && dataSource["itemList"].includes(1003)) {
    showNode('layout2', json);
  } else if (dataSource["itemList"].includes(1001) && dataSource["itemList"].includes(1002)) {
    showNode('layout3', json);
  }

  // 通用信息
  // 陪伴天数
  replaceText('day_num', `${dataSource["dayNum"]}`, json);
  // 宠物图片
  replaceImage('pet', dataSource["petPic"], json);
  // 宠物台词
  replaceText('sentences', dataSource["sentences"], json);

  // 基础数据
  if (dataSource["itemList"].includes(1001)) {
    // 次数
    replaceText('times', `${dataSource["timesCurValue"]}/${dataSource["timesMaxValue"]}`, json);
    // 萌爪币数量
    replaceText('coin_num', `${dataSource["coinCurValue"]}/${dataSource["coinMaxValue"]}`, json);
  }


  // 宠物繁育信息
  if (dataSource["itemList"].includes(1002)) {
    // 宠物繁育信息
    if (!dataSource["petList"] || dataSource["petList"].length == 0) {
      hideNodes(['grow1','grow2'], json);
      showNode('grow0', json);
    } else if (dataSource["petList"].length == 1) {
      hideNodes(['grow0','grow2'], json);
      showNode('grow1', json);
    } else if (dataSource["petList"].length == 2) {
      hideNodes(['grow0','grow1'], json);
      showNode('grow2', json);
    }
    dataSource["petList"].forEach((pet: any, index: number) => {
      replaceImage(`headicon${index+1}`, pet.headIcon, json);
      replaceText(`time${index+1}`, pet.time, json);

      if (isLarge) {
        replaceText(`status${index+1}`, pet.status, json);
        replaceImage(`status_bg${index+1}`, pet.statusBg, json);
      }
    });
  }

  // 外观因子布局计算
  if (dataSource["itemList"].includes(1003)) {
    let offsetX = 0;
    let offsetY = 0;
    hideNodes(['factor_item1','factor_item2', 'factor_item3', 'factor_item4', 'factor_item5', 'factor_item6', 'factor_item7'], json);

    // 根据字数判断从哪里开始分两行，输出需要换行的位置
    let curWidth = 0;
    let splitIndex = 0;
    for (let i = 0; i < dataSource["factorsList"].length; i++) {
      curWidth += dataSource["factorsList"][i].name.length*10 + 14 + 3;
      splitIndex = i;
      if (curWidth > 270 || i >= 5) {
        break;
      }
    }
    // 设置内容
    dataSource["factorsList"].forEach((factor: any, index: number) => {
      showNode(`factor_item${index+1}`, json);
      replaceImage(`factor_bg${index+1}`, factor.name_bg, json);
      replaceText(`factor${index+1}`, factor.name, json);
      let itemWidth = factor.name.length * 10 + 14;
      changeNodeWidth(`factor_bg${index+1}`, itemWidth, json);
      changeNodeWidth(`factor${index+1}`, itemWidth, json);

      changeNodePosition(`factor_bg${index+1}`, offsetX, offsetY, json);
      changeNodePosition(`factor${index+1}`, offsetX, offsetY, json);

      // 计算偏移量
      if (index < splitIndex - 1) {
        offsetX += itemWidth + 3;
        offsetY = 0;
      } else if (index == splitIndex - 1){
        offsetX =  0;
        offsetY = 20 * (isLarge ? -1 : 1);
      } else {
        offsetX += itemWidth + 3;
        offsetY = 20 * (isLarge ? -1 : 1);
      }
    });
  }
}