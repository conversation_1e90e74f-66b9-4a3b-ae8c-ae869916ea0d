export function changeCommonWidgetJson(viewJsonObj: JSON, appId: string, type: number, json: JSON) {
  if ("appId" in viewJsonObj) {
    viewJsonObj["appId"] = appId;
  }
  return viewJsonObj;
}

// 根据name字符串获取节点
export const getNodeByName = (name: string, json: JSON) => {
  let elements = json["elements"];
  let result: any[] = []
  for (let key in elements) {
    if (elements[key].name === name) {
      result.push(elements[key])
    }
  }
  return result
}


// 替换文本
export const replaceText = (nodeName: string, text: string, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.content = text;
  })
}

// 替换图片
export const replaceImage = (nodeName: string, imageUrl: string, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.src = imageUrl;
  })
}

// 隐藏节点
export const hideNode = (nodeName: string, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.hide = true;
  })
}


export const hideNodes = (nodeName: Array<string>, json: JSON) => {
  nodeName.forEach(name => {
    let elements = getNodeByName(name, json);
    elements.forEach(element => {
      element.hide = true;
    })
  })
}

// 显示节点
export const showNode = (nodeName: string, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.hide = false;
  })
}

// 修改节点定位
export const changeNodePosition = (nodeName: string, offsetX: number, offsetY: number, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.x = element.x+offsetX;
    element.y = element.y+offsetY;
  })
}

// 修改节点大小
export const changeNodeSize = (nodeName: string, width: number, height: number, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.width = width;
    element.height = height;
  })
}

// 修改节点宽度
export const changeNodeWidth = (nodeName: string, width: number, json: JSON) => {
  let elements = getNodeByName(nodeName, json);
  elements.forEach(element => {
    element.width = width;
  })
}

export const isDeviceClickAvailable = (dataJson: JSON) => {
  let deviceInfo = dataJson["device_info"];
  // 如果是iOS且版本号小于17.0，则不支持点击
  if (deviceInfo["systemName"].toLowerCase() === "ios") { 
    let version = deviceInfo["systemVersion"];
    let versionArr = version.split(".");
    if (Number(versionArr[0]) < 17) {
      return false;
    } else {
      return true;
    }
  } else {
    return true;
  }
}