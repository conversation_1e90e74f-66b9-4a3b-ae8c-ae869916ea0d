import { changeOOTDWidgetJson } from "./ootd";
import { changePetWidgetJson } from "./pet";
import { changeWeatherWidgetJson } from "./weather";

enum EnumDesktopWidgetType {
  Basic = 1, // 日常助手
  DressUp = 2, // 穿搭
  Weather = 3, // 天气
  Calendar = 4, // 日历
  CompanionBase = 5, // 专属陪伴 基础
  CompanionNpcIM = 6, // 专属陪伴 好感
  CompanionPet = 7, // 专属陪伴 宠物
}


export function changeWidgetJson(viewJson: string, appId: string, type: number, size: number, json: JSON) {
  let result: JSON;
  switch (type) {
    case EnumDesktopWidgetType.Basic:
      break;
    case EnumDesktopWidgetType.DressUp:
      result = changeOOTDWidgetJson(viewJson, appId, type, size, json);
      break;
    case EnumDesktopWidgetType.Weather:
      result = changeWeatherWidgetJson(viewJson, appId, type, size, json);
      break;
    case EnumDesktopWidgetType.Calendar:
      break;
    case EnumDesktopWidgetType.CompanionBase:
      break;
    case EnumDesktopWidgetType.CompanionNpcIM:
      break;
    case EnumDesktopWidgetType.CompanionPet:
      result = changePetWidgetJson(viewJson, appId, type, size, json);
      break;
  }
  return result
}

export function isValidJSONObject(str) {
  try {
    const parsed = JSON.parse(str);
    return typeof parsed === 'object' && parsed !== null;
  } catch (e) {
    return false;
  }
}
