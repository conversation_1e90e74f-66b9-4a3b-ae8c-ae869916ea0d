import { changeCommonWidgetJson, changeNodePosition, hideNode, replaceImage, replaceText } from "./common";
import { EnumDesktopWidgetSize, EnumWeatherCustomType } from "./constant";

export function changeWeatherWidgetJson(viewJson: string, appId: string, type: number, size: number, dataJson: JSON) {
  let viewJsonObj = JSON.parse(viewJson);
  changeCommonWidgetJson(viewJsonObj, appId, type, dataJson);

  if (size == EnumDesktopWidgetSize.Medium) {
    parseJsonWeatherMedium(viewJsonObj, dataJson);
  } else if (size == EnumDesktopWidgetSize.Small) {
    parseJsonWeatherSmall(viewJsonObj, dataJson);
  }

  return viewJsonObj;
}


function parseJsonWeatherMedium(json: JSON, dataSource: JSON) {
  commonContentReplace(json, dataSource);

  if (dataSource["custom_type"] == EnumWeatherCustomType.Scene) {
    // 隐藏台词
    hideNode('dialogue', json);
  } else {
    // 修改台词
    replaceText('dialogue_text', dataSource["dialogue"], json);
  }


  // 计算日期和分割线位置
  let offsetX = (dataSource["weather"].condition.length - 2) * 11;
  let offsetY = 0;
  changeNodePosition('date', offsetX, offsetY, json);
  changeNodePosition('split_line', offsetX, offsetY, json);
}


function parseJsonWeatherSmall(json: JSON, dataSource: JSON): any {
  commonContentReplace(json, dataSource);
}

function commonContentReplace(json: JSON, dataSource: JSON): any {
  // 修改天气
  replaceText('weather_text', dataSource["weather"].condition, json);

  // 修改天气图标
  replaceImage('weather_icon', dataSource["weather"].conditionIcon, json);

  // 修改温度
  replaceText('temperature', dataSource["weather"].temperature+'°', json);

  // 修改背景
  replaceImage('bg', dataSource["background_image"], json);

  // 修改位置
  replaceText('location', dataSource["location"].city, json);

  // 修改日期
  replaceText('date', dataSource["date"], json);
}