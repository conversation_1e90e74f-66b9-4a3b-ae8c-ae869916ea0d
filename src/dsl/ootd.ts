import { changeCommonWidgetJson, changeNodePosition, changeNodeWidth, hideNode, hideNodes, isDeviceClickAvailable, replaceImage, replaceText } from "./common";
import { EnumDesktopWidgetSize } from "./constant";

export function changeOOTDWidgetJson(viewJson: string, appId: string, type: number, size: number, dataJson: JSON) {
  let viewJsonObj = JSON.parse(viewJson);
  changeCommonWidgetJson(viewJsonObj, appId, type, dataJson);

  commonContentReplace(viewJsonObj, dataJson);

  let fontSize = size == EnumDesktopWidgetSize.Medium ? 11 : 12;
  let padding = size == EnumDesktopWidgetSize.Medium ? 8 : 12;
  if (dataJson["show_activity"]) {
    // 修改文案和背景宽度
    let curWidth = dataJson["activity"].length * fontSize + 2*padding;
    changeNodeWidth('activity_text', curWidth, viewJsonObj);
    changeNodeWidth('activity_bg', curWidth, viewJsonObj);
    // 修改文案文案和背景位置
    let baseWidth = size == EnumDesktopWidgetSize.Medium ? 154 : 174;
    let offsetX = baseWidth - curWidth;
    let offsetY = 0;
    changeNodePosition('activity_text', offsetX, offsetY, viewJsonObj);
    changeNodePosition('activity_bg', offsetX, offsetY, viewJsonObj);
  }

  return viewJsonObj;
}


function commonContentReplace(json: JSON, dataSource: JSON): any {
  // 修改热度值
  replaceText('hot', dataSource["hot"], json);

  // 修改收藏图标
  replaceImage('pick', dataSource["pic"], json);

  // 修改作品名
  replaceText('pic_name', dataSource["pic_name"], json);

  // 修改背景
  replaceImage('bg', dataSource["background_image"], json);

  if (dataSource["show_activity"]) {
    // 修改活动文案
    replaceText('activity_text', dataSource["activity"].city, json);
  } else {
    hideNode('activity', json);
  }

  // 如果系统版本不足17，隐藏交互按钮
  if (isDeviceClickAvailable(dataSource)) {
    hideNodes(['prev', 'next', 'pick'], json);
    changeNodePosition('pic_name', -24, 0, json);
  }
}