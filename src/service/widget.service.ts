import { Provide } from '@midwayjs/core';
import { IGetWidgetOptions, IChangeWidgetOptions } from '../interface';
import { changeWidgetJson, isValidJSONObject } from '../dsl'
import { PlayerWidget } from '../entity/PlayerWidget';
import { Repository } from 'typeorm';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { WidgetView } from '../entity/WidgetView';

@Provide()
export class WidgetService {

  @InjectEntityModel(PlayerWidget)
  playerWidgetModel: Repository<PlayerWidget>;

  @InjectEntityModel(WidgetView)
  widgetViewModel: Repository<WidgetView>;

  async getPlayerWidget(options: IGetWidgetOptions) {
    // find first
    let firstWidget = await this.playerWidgetModel.findOne({
      where: {
        widgetId: options.appId,
        roleId: options.roleId,
        isDeleted: 0,
      }
    });
    if (firstWidget) {
      return JSON.parse(firstWidget.viewJson);
    }
    return firstWidget;
  }

  async getPlayerWidgetOptions(options: IGetWidgetOptions) {
    let firstWidget = await this.playerWidgetModel.findOne({
      where: {
        widgetId: options.appId,
        roleId: options.roleId,
        isDeleted: 0,
      }
    });
    if (firstWidget) {
      return {
        widgetType: firstWidget.widgetType,
        widgetSize: firstWidget.widgetSize,
      };
    }
    return firstWidget;
  }

  async changeWidgetJSON(options: IChangeWidgetOptions) {
    // find first
    let widgetOptions = await this.getPlayerWidgetOptions(options);
    let firstWidget = await this.widgetViewModel.findOne({
      where: {
        widgetType: widgetOptions.widgetType,
        widgetSize: widgetOptions.widgetSize,
      }
    });
    if (firstWidget) {
      if (isValidJSONObject(options.setting)) {
        const setting = JSON.parse(options.setting);
        const result = changeWidgetJson(firstWidget.viewJson, options.appId, firstWidget.widgetType, firstWidget.widgetSize, setting);
        if (result) {
          console.log('changeResult', result);
          firstWidget.viewJson = JSON.stringify(result, null, 2);
          this.playerWidgetModel.save(firstWidget);
          return result;
        }
      }
    }
    return null
  }
}

