import { Inject, Controller, Get, Query, Post, Body } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { WidgetService } from '../service/widget.service';
import { WidgetGetDTO, WidgetPostDTO } from '../dto/widget';

@Controller('/widget')
export class WidgetController {
  @Inject()
  ctx: Context;

  @Inject()
  widgetService: WidgetService;

  @Get('/getjson')
  async getJson(@Query() request: WidgetGetDTO) {
    const user = await this.widgetService.getPlayerWidget({ appId: request.appId, roleId: request.roleId });
    return { success: true, message: 'OK', data: user };
  }

  @Post('/changejson')
  async changeJson(@Body() request: WidgetPostDTO) {
    const body = this.ctx.request.body;
    this.ctx.logger.info(
      `changeJson body ${
        JSON.stringify(body)
      }`
    );
    this.ctx.logger.info(
      `changeJson appId ${
        request.appId
      }`
    );
    const user = await this.widgetService.changeWidgetJSON({ appId: request.appId, roleId: request.roleId, setting: request.setting });
    return { success: true, message: 'OK', data: user };
  }
}
