/**
 * @description User-Service parameters
 */
export interface IUserOptions {
  uid: number;
}

export interface WeatherInfo {
  weatherinfo: {
    city: string;
    cityid: string;
    temp: string;
    WD: string;
    WS: string;
    SD: string;
    AP: string;
    njd: string;
    WSE: string;
    time: string;
    sm: string;
    isRadar: string;
    Radar: string;
  };
}

export interface IGetWidgetOptions {
  appId: string;
  roleId: string;
}

export interface IChangeWidgetOptions {
  appId: string;
  roleId: string;
  setting: string;
}
