import { MidwayConfig } from '@midwayjs/core';

export default {
  // use for cookie sign key, should change to your own and keep security
  keys: '1758008855563_8729',
  koa: {
    port: 7001,
  },
  view: {
    defaultViewEngine: 'nunjucks',
  },
  typeorm: {
    dataSource: {
      // default1: {
      //   type: 'mysql',
      //   host: '127.0.0.1',
      //   port: 3306,
      //   username: 'root',
      //   password: 'czl123456',
      //   database: 'small_component',
      //   synchronize: false,
      //   logging: true,
      //   // 支持如下的扫描形式，为了兼容我们可以同时进行.js和.ts匹配
      //   entities: [
      //     'entity',
      //   ]
      // },
      default2: {
        type: 'mysql',
        host: 'apps-sl.danlu.netease.com',
        port: 35667,
        username: 'root',
        password: '5dc85310fbc7a064262478ab021d090e',
        database: 'small_component',
        synchronize: false,
        logging: false,
        // 支持如下的扫描形式，为了兼容我们可以同时进行.js和.ts匹配
        entities: [
          'entity',
        ]
      }
    },
    defaultDataSourceName: 'default2',
  },
} as MidwayConfig;
