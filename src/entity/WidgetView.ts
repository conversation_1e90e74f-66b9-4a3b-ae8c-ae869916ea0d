import { Column, Index, PrimaryGeneratedColumn, Entity } from "typeorm";

@Index("uk_view", ["widgetType", "widgetSize", "viewType", "designId"], {})
@Entity("widget_view", { schema: "small_component" })
export class WidgetView {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", comment: "主键" })
  id: string;

  @Column("int", { name: "widget_type", comment: "组件类型" })
  widgetType: number;

  @Column("tinyint", { name: "widget_size", comment: "组件尺寸" })
  widgetSize: number;

  @Column("varchar", {
    name: "view_type",
    comment: "组件视图类型",
    length: 255,
  })
  viewType: string;

  @Column("varchar", {
    name: "view_name",
    comment: "组件应用名称",
    length: 255,
  })
  viewName: string;

  @Column("varchar", { name: "design_name", comment: "设计名称", length: 255 })
  designName: string;

  @Column("bigint", { name: "design_id", comment: "设计版本ID" })
  designId: string;

  @Column("longtext", { name: "view_json", comment: "视图JSON模板" })
  viewJson: string;

  @Column("tinyint", {
    name: "status",
    comment: "状态：1-启用，0-禁用",
    default: () => "'1'",
  })
  status: number;

  @Column("bigint", {
    name: "ctime",
    comment: "创建时间",
    default: () => "'0'",
  })
  ctime: string;

  @Column("bigint", {
    name: "utime",
    comment: "更新时间",
    default: () => "'0'",
  })
  utime: string;

  @Column("varchar", {
    name: "description",
    comment: "组件视图描述",
    length: 255,
  })
  description: string;

  @Column("varchar", { name: "icon", comment: "组件视图icon", length: 255 })
  icon: string;
}
