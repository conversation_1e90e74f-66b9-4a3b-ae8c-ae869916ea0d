import { Column, PrimaryGeneratedColumn, Entity } from "typeorm";

@Entity("info", { schema: "small_component" })
export class Info {
  @PrimaryGeneratedColumn({ type: "int", name: "id", unsigned: true })
  id: number;

  @Column("longtext", { name: "domain", nullable: true })
  domain: string | null;

  @Column("longtext", { name: "game_id", nullable: true })
  gameId: string | null;

  @Column("longtext", { name: "app_id", nullable: true })
  appId: string | null;

  @Column("longtext", { name: "role_id", nullable: true })
  roleId: string | null;

  @Column("bigint", { name: "nums", nullable: true })
  nums: string | null;
}
