import { Column, PrimaryGeneratedColumn, Entity } from "typeorm";

@Entity("player_widget", { schema: "small_component" })
export class PlayerWidget {
  @PrimaryGeneratedColumn({ type: "bigint", name: "id", comment: "主键" })
  id: string;

  @Column("bigint", { name: "role_id", comment: "角色ID" })
  roleId: string;

  @Column("varchar", { name: "widget_id", comment: "组件实例ID", length: 255 })
  widgetId: string;

  @Column("int", { name: "widget_type", comment: "组件类型" })
  widgetType: number;

  @Column("tinyint", { name: "widget_size", comment: "组件尺寸" })
  widgetSize: number;

  @Column("varchar", {
    name: "widget_name",
    nullable: true,
    comment: "组件名称",
    length: 255,
  })
  widgetName: string | null;

  @Column("varchar", {
    name: "item_id_list",
    comment: "选择的业务字段ID列表（JSON数组）",
    length: 1000,
  })
  itemIdList: string;

  @Column("varchar", {
    name: "bg_id_list",
    nullable: true,
    comment: "背景ID列表",
    length: 500,
  })
  bgIdList: string | null;

  @Column("text", {
    name: "select_object_info",
    nullable: true,
    comment: "选择的对象信息（JSON）",
  })
  selectObjectInfo: string | null;

  @Column("text", {
    name: "pre_setting_log",
    nullable: true,
    comment: "预设置日志（包含MaxValue和CurValue）",
  })
  preSettingLog: string | null;

  @Column("varchar", {
    name: "matched_view_type",
    comment: "匹配到的小组件视图类型",
    length: 255,
  })
  matchedViewType: string;

  @Column("tinyint", {
    name: "is_deleted",
    comment: "是否已删除",
    default: () => "'0'",
  })
  isDeleted: number;

  @Column("bigint", {
    name: "ctime",
    comment: "创建时间",
    default: () => "'0'",
  })
  ctime: string;

  @Column("bigint", {
    name: "utime",
    comment: "更新时间",
    default: () => "'0'",
  })
  utime: string;

  @Column("text", {
    name: "selectq_image_info",
    nullable: true,
    comment: "选择的对象信息（JSON）",
  })
  selectqImageInfo: string | null;

  @Column("longtext", { name: "view_json", comment: "角色的视图JSON模板" })
  viewJson: string;
}
